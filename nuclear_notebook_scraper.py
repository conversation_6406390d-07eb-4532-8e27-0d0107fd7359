#!/usr/bin/env python3
"""
Nuclear Notebook PDF Scraper
自动下载原子科学家公报核武器库所有文章的PDF工具
"""

import requests
from bs4 import BeautifulSoup
import time
import os
import re
from urllib.parse import urlparse
import base64
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import logging
import csv
import json
import pandas as pd

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class NuclearNotebookScraper:
    def __init__(self, output_dir="nuclear_notebook_pdfs"):
        self.base_url = "https://thebulletin.org/nuclear-risk/nuclear-weapons/nuclear-notebook"
        self.output_dir = output_dir
        self.session = requests.Session()

        # 更完整的请求头，模拟真实浏览器
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0'
        })

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)

        # 设置Chrome选项
        self.chrome_options = Options()
        self.chrome_options.add_argument('--headless')
        self.chrome_options.add_argument('--no-sandbox')
        self.chrome_options.add_argument('--disable-dev-shm-usage')
        self.chrome_options.add_argument('--disable-gpu')
        self.chrome_options.add_argument('--window-size=1920,1080')
        self.chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        self.chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        self.chrome_options.add_experimental_option('useAutomationExtension', False)
        
    def get_all_article_links(self):
        """获取所有13页的文章链接"""
        all_links = []

        # 创建WebDriver实例
        driver = webdriver.Chrome(options=self.chrome_options)

        try:
            # 添加反检测脚本
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            for page_num in range(1, 14):  # 1到13页
                if page_num == 1:
                    page_url = self.base_url
                else:
                    page_url = f"{self.base_url}/page/{page_num}/"

                logger.info(f"正在获取第{page_num}页的文章链接: {page_url}")

                try:
                    driver.get(page_url)

                    # 等待页面加载
                    WebDriverWait(driver, 15).until(
                        EC.presence_of_element_located((By.TAG_NAME, "body"))
                    )

                    # 滚动页面确保所有内容加载
                    driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                    time.sleep(3)

                    # 获取页面源码并解析
                    soup = BeautifulSoup(driver.page_source, 'html.parser')

                    # 查找文章链接 - 更精确的选择器
                    page_links = []

                    # 查找文章标题链接
                    article_elements = soup.find_all('h2') + soup.find_all('h3')

                    for element in article_elements:
                        link = element.find('a', href=True)
                        if link:
                            href = link.get('href')
                            title = link.get_text(strip=True)

                            if href and title and len(title) > 5:  # 过滤掉太短的标题
                                # 过滤掉不是文章的链接
                                if any(exclude in href for exclude in ['/page/', '/category/', '/tag/', '/author/', '/about-us/', '/contact-us/']):
                                    continue

                                # 确保是完整URL
                                if href.startswith('/'):
                                    full_url = f"https://thebulletin.org{href}"
                                elif href.startswith('http'):
                                    full_url = href
                                else:
                                    continue

                                # 检查是否已存在
                                if full_url not in [item['url'] for item in all_links]:
                                    page_links.append({
                                        'title': title,
                                        'url': full_url,
                                        'page': page_num
                                    })

                    all_links.extend(page_links)
                    logger.info(f"第{page_num}页找到 {len(page_links)} 篇文章")

                    # 添加延迟避免被封
                    time.sleep(5)

                except Exception as e:
                    logger.error(f"获取第{page_num}页时出错: {e}")
                    continue

        finally:
            driver.quit()

        logger.info(f"总共找到 {len(all_links)} 篇文章")
        return all_links

    def save_links_to_table(self, articles):
        """将文章链接保存到表格文件"""
        if not articles:
            logger.error("没有文章链接可保存")
            return False

        # 保存为CSV格式
        csv_file = os.path.join(self.output_dir, "articles_list.csv")
        try:
            with open(csv_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow(['序号', '页码', '标题', 'URL'])
                for i, article in enumerate(articles, 1):
                    writer.writerow([i, article['page'], article['title'], article['url']])
            logger.info(f"文章列表已保存到: {csv_file}")
        except Exception as e:
            logger.error(f"保存CSV文件时出错: {e}")
            return False

        # 保存为JSON格式
        json_file = os.path.join(self.output_dir, "articles_list.json")
        try:
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(articles, f, ensure_ascii=False, indent=2)
            logger.info(f"文章列表已保存到: {json_file}")
        except Exception as e:
            logger.error(f"保存JSON文件时出错: {e}")
            return False

        return True

    def load_links_from_table(self):
        """从表格文件加载文章链接"""
        json_file = os.path.join(self.output_dir, "articles_list.json")
        if os.path.exists(json_file):
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    articles = json.load(f)
                logger.info(f"从 {json_file} 加载了 {len(articles)} 篇文章")
                return articles
            except Exception as e:
                logger.error(f"加载JSON文件时出错: {e}")

        csv_file = os.path.join(self.output_dir, "articles_list.csv")
        if os.path.exists(csv_file):
            try:
                articles = []
                with open(csv_file, 'r', encoding='utf-8') as f:
                    reader = csv.DictReader(f)
                    for row in reader:
                        articles.append({
                            'title': row['标题'],
                            'url': row['URL'],
                            'page': int(row['页码'])
                        })
                logger.info(f"从 {csv_file} 加载了 {len(articles)} 篇文章")
                return articles
            except Exception as e:
                logger.error(f"加载CSV文件时出错: {e}")

        return None
    
    def clean_filename(self, filename):
        """清理文件名，移除非法字符"""
        # 移除或替换非法字符
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        # 限制长度
        if len(filename) > 200:
            filename = filename[:200]
        return filename.strip()
    
    def save_article_as_pdf(self, article_info):
        """将文章保存为PDF"""
        url = article_info['url']
        title = article_info['title']
        page_num = article_info['page']

        # 创建安全的文件名
        safe_title = self.clean_filename(title)
        filename = f"Page{page_num:02d}_{safe_title}.pdf"
        filepath = os.path.join(self.output_dir, filename)

        # 如果文件已存在，跳过
        if os.path.exists(filepath):
            logger.info(f"文件已存在，跳过: {filename}")
            return True

        logger.info(f"正在下载: {title}")

        driver = None
        try:
            # 配置Chrome选项用于PDF生成
            chrome_options = Options()
            chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)

            # 添加PDF打印设置
            chrome_options.add_argument('--enable-print-browser')
            chrome_options.add_argument('--run-all-compositor-stages-before-draw')
            chrome_options.add_argument('--disable-background-timer-throttling')
            chrome_options.add_argument('--disable-renderer-backgrounding')
            chrome_options.add_argument('--disable-backgrounding-occluded-windows')

            driver = webdriver.Chrome(options=chrome_options)

            # 添加反检测脚本
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            driver.get(url)

            # 等待页面加载
            WebDriverWait(driver, 15).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )

            # 等待文章内容加载
            time.sleep(5)

            # 滚动到页面底部确保所有内容加载
            driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(3)

            # 尝试移除不必要的元素（广告、导航等）
            try:
                driver.execute_script("""
                    // 移除可能的弹窗和广告
                    var elements = document.querySelectorAll('nav, header, footer, .advertisement, .popup, .modal, .sidebar');
                    elements.forEach(function(el) {
                        if(el) el.style.display = 'none';
                    });

                    // 移除固定定位的元素
                    var fixedElements = document.querySelectorAll('*');
                    fixedElements.forEach(function(el) {
                        var style = window.getComputedStyle(el);
                        if(style.position === 'fixed' || style.position === 'sticky') {
                            el.style.display = 'none';
                        }
                    });
                """)
            except:
                pass

            # 使用Chrome的打印功能生成PDF
            print_options = {
                'landscape': False,
                'displayHeaderFooter': False,
                'printBackground': True,
                'preferCSSPageSize': True,
                'paperWidth': 8.27,  # A4 width in inches
                'paperHeight': 11.69,  # A4 height in inches
                'marginTop': 0.4,
                'marginBottom': 0.4,
                'marginLeft': 0.4,
                'marginRight': 0.4
            }

            # 生成PDF
            pdf_data = driver.execute_cdp_cmd('Page.printToPDF', print_options)

            # 保存PDF文件
            with open(filepath, 'wb') as f:
                f.write(base64.b64decode(pdf_data['data']))

            logger.info(f"成功保存: {filename}")
            return True

        except Exception as e:
            logger.error(f"保存文章 '{title}' 时出错: {e}")
            return False

        finally:
            if driver:
                driver.quit()
            # 添加延迟
            time.sleep(3)
    
    def step1_collect_links(self):
        """第一步：收集所有文章链接并保存到表格"""
        logger.info("=== 第一步：开始获取所有文章链接 ===")
        articles = self.get_all_article_links()

        if not articles:
            logger.error("未找到任何文章")
            return False

        # 保存链接到表格
        if self.save_links_to_table(articles):
            logger.info(f"=== 第一步完成：成功收集并保存了 {len(articles)} 篇文章链接 ===")
            return True
        else:
            logger.error("保存文章链接失败")
            return False

    def step2_download_pdfs(self):
        """第二步：根据表格下载PDF"""
        logger.info("=== 第二步：开始下载PDF ===")

        # 从表格加载文章链接
        articles = self.load_links_from_table()
        if not articles:
            logger.error("未找到文章链接表格，请先运行第一步")
            return False

        logger.info(f"开始下载 {len(articles)} 篇文章...")

        success_count = 0
        for i, article in enumerate(articles, 1):
            logger.info(f"进度: {i}/{len(articles)}")
            if self.save_article_as_pdf(article):
                success_count += 1

        logger.info(f"=== 第二步完成：下载完成! 成功: {success_count}/{len(articles)} ===")
        return success_count > 0

    def run_step1_only(self):
        """只运行第一步"""
        return self.step1_collect_links()

    def run_step2_only(self):
        """只运行第二步"""
        return self.step2_download_pdfs()

    def run_both_steps(self):
        """运行两个步骤"""
        if self.step1_collect_links():
            return self.step2_download_pdfs()
        return False

def main():
    """主函数"""
    import sys

    scraper = NuclearNotebookScraper()

    if len(sys.argv) > 1:
        if sys.argv[1] == "step1":
            scraper.run_step1_only()
        elif sys.argv[1] == "step2":
            scraper.run_step2_only()
        elif sys.argv[1] == "both":
            scraper.run_both_steps()
        else:
            print("用法:")
            print("  python nuclear_notebook_scraper.py step1  # 只运行第一步：收集链接")
            print("  python nuclear_notebook_scraper.py step2  # 只运行第二步：下载PDF")
            print("  python nuclear_notebook_scraper.py both   # 运行两个步骤")
    else:
        # 默认运行第一步
        print("默认运行第一步：收集文章链接")
        print("如需下载PDF，请运行: python nuclear_notebook_scraper.py step2")
        scraper.run_step1_only()

if __name__ == "__main__":
    main()
