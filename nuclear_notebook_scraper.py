#!/usr/bin/env python3
"""
Nuclear Notebook PDF Scraper
自动下载原子科学家公报核武器库所有文章的PDF工具
"""

import requests
from bs4 import BeautifulSoup
import time
import os
import re
from urllib.parse import urlparse
import pdfkit
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class NuclearNotebookScraper:
    def __init__(self, output_dir="nuclear_notebook_pdfs"):
        self.base_url = "https://thebulletin.org/nuclear-risk/nuclear-weapons/nuclear-notebook"
        self.output_dir = output_dir
        self.session = requests.Session()

        # 更完整的请求头，模拟真实浏览器
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0'
        })

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)

        # 设置Chrome选项
        self.chrome_options = Options()
        self.chrome_options.add_argument('--headless')
        self.chrome_options.add_argument('--no-sandbox')
        self.chrome_options.add_argument('--disable-dev-shm-usage')
        self.chrome_options.add_argument('--disable-gpu')
        self.chrome_options.add_argument('--window-size=1920,1080')
        self.chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        self.chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        self.chrome_options.add_experimental_option('useAutomationExtension', False)
        
    def get_all_article_links(self):
        """获取所有13页的文章链接"""
        all_links = []

        # 创建WebDriver实例
        driver = webdriver.Chrome(options=self.chrome_options)

        try:
            # 添加反检测脚本
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            for page_num in range(1, 14):  # 1到13页
                if page_num == 1:
                    page_url = self.base_url
                else:
                    page_url = f"{self.base_url}/page/{page_num}/"

                logger.info(f"正在获取第{page_num}页的文章链接: {page_url}")

                try:
                    driver.get(page_url)

                    # 等待页面加载
                    WebDriverWait(driver, 15).until(
                        EC.presence_of_element_located((By.TAG_NAME, "body"))
                    )

                    # 滚动页面确保所有内容加载
                    driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                    time.sleep(3)

                    # 获取页面源码并解析
                    soup = BeautifulSoup(driver.page_source, 'html.parser')

                    # 查找文章链接 - 更精确的选择器
                    page_links = []

                    # 查找文章标题链接
                    article_elements = soup.find_all('h2') + soup.find_all('h3')

                    for element in article_elements:
                        link = element.find('a', href=True)
                        if link:
                            href = link.get('href')
                            title = link.get_text(strip=True)

                            if href and title and len(title) > 5:  # 过滤掉太短的标题
                                # 过滤掉不是文章的链接
                                if any(exclude in href for exclude in ['/page/', '/category/', '/tag/', '/author/', '/about-us/', '/contact-us/']):
                                    continue

                                # 确保是完整URL
                                if href.startswith('/'):
                                    full_url = f"https://thebulletin.org{href}"
                                elif href.startswith('http'):
                                    full_url = href
                                else:
                                    continue

                                # 检查是否已存在
                                if full_url not in [item['url'] for item in all_links]:
                                    page_links.append({
                                        'title': title,
                                        'url': full_url,
                                        'page': page_num
                                    })

                    all_links.extend(page_links)
                    logger.info(f"第{page_num}页找到 {len(page_links)} 篇文章")

                    # 添加延迟避免被封
                    time.sleep(5)

                except Exception as e:
                    logger.error(f"获取第{page_num}页时出错: {e}")
                    continue

        finally:
            driver.quit()

        logger.info(f"总共找到 {len(all_links)} 篇文章")
        return all_links
    
    def clean_filename(self, filename):
        """清理文件名，移除非法字符"""
        # 移除或替换非法字符
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        # 限制长度
        if len(filename) > 200:
            filename = filename[:200]
        return filename.strip()
    
    def save_article_as_pdf(self, article_info):
        """将文章保存为PDF"""
        url = article_info['url']
        title = article_info['title']
        page_num = article_info['page']

        # 创建安全的文件名
        safe_title = self.clean_filename(title)
        filename = f"Page{page_num:02d}_{safe_title}.pdf"
        filepath = os.path.join(self.output_dir, filename)

        # 如果文件已存在，跳过
        if os.path.exists(filepath):
            logger.info(f"文件已存在，跳过: {filename}")
            return True

        logger.info(f"正在下载: {title}")

        driver = None
        try:
            # 使用Selenium获取完整渲染的页面
            driver = webdriver.Chrome(options=self.chrome_options)

            # 添加反检测脚本
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            driver.get(url)

            # 等待页面加载
            WebDriverWait(driver, 15).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )

            # 等待文章内容加载
            time.sleep(5)

            # 滚动到页面底部确保所有内容加载
            driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(3)

            # 尝试移除不必要的元素（广告、导航等）
            try:
                driver.execute_script("""
                    // 移除可能的弹窗和广告
                    var elements = document.querySelectorAll('nav, header, footer, .advertisement, .popup, .modal');
                    elements.forEach(function(el) { el.style.display = 'none'; });
                """)
            except:
                pass

            # 获取页面HTML
            html_content = driver.page_source

            # 配置PDF选项
            options = {
                'page-size': 'A4',
                'margin-top': '0.75in',
                'margin-right': '0.75in',
                'margin-bottom': '0.75in',
                'margin-left': '0.75in',
                'encoding': "UTF-8",
                'no-outline': None,
                'enable-local-file-access': None,
                'load-error-handling': 'ignore',
                'load-media-error-handling': 'ignore'
            }

            # 转换为PDF
            pdfkit.from_string(html_content, filepath, options=options)
            logger.info(f"成功保存: {filename}")
            return True

        except Exception as e:
            logger.error(f"保存文章 '{title}' 时出错: {e}")
            return False

        finally:
            if driver:
                driver.quit()
            # 添加延迟
            time.sleep(5)
    
    def run(self):
        """运行爬虫"""
        logger.info("开始获取所有文章链接...")
        articles = self.get_all_article_links()
        
        if not articles:
            logger.error("未找到任何文章")
            return
        
        logger.info(f"开始下载 {len(articles)} 篇文章...")
        
        success_count = 0
        for i, article in enumerate(articles, 1):
            logger.info(f"进度: {i}/{len(articles)}")
            if self.save_article_as_pdf(article):
                success_count += 1
        
        logger.info(f"下载完成! 成功: {success_count}/{len(articles)}")

def main():
    """主函数"""
    scraper = NuclearNotebookScraper()
    scraper.run()

if __name__ == "__main__":
    main()
